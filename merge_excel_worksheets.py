import pandas as pd
import os
from pathlib import Path
import glob
from rich.console import Console
from rich.progress import Progress, BarColumn, TextColumn, TimeElapsedColumn
from rich.panel import Panel
from rich.text import Text

console = Console()

def merge_excel_worksheets():
    """
    Merge worksheets from multiple Excel workbooks based on worksheet names.
    Each worksheet type will be merged into a single file.
    """
    
    # Source directory containing Excel workbooks
    source_dir = r"H:\mailwizz\mathews mailing\conferences\For Kutools"
    
    # Output directory for merged files
    output_dir = os.path.join(source_dir, "merged")
    
    # Expected worksheet names
    worksheet_names = [
        "Valid+BasicCheck+DEA",
        "CatchAll_AcceptAll", 
        "Invalid",
        "Invalid Typo Bad",
        "Role"
    ]
    
    # Check if source directory exists
    if not os.path.exists(source_dir):
        console.print(f"[red]Error: Source directory not found: {source_dir}[/red]")
        return
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    console.print(f"[green]Output directory: {output_dir}[/green]")
    
    # Find all Excel files in the source directory
    excel_files = glob.glob(os.path.join(source_dir, "*.xlsx"))
    
    if not excel_files:
        console.print(f"[red]No Excel files found in {source_dir}[/red]")
        return
    
    console.print(f"[blue]Found {len(excel_files)} Excel files to process[/blue]")
    
    # Dictionary to store merged data for each worksheet type
    merged_data = {sheet_name: [] for sheet_name in worksheet_names}
    
    # Process each Excel file
    with Progress(
        TextColumn("[progress.description]{task.description}"),
        BarColumn(bar_width=None, style="bar.back", complete_style="green", finished_style="green"),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
        TimeElapsedColumn(),
        console=console
    ) as progress:
        
        main_task = progress.add_task("Processing Excel files...", total=len(excel_files))
        
        for excel_file in excel_files:
            file_name = os.path.basename(excel_file)
            progress.update(main_task, description=f"Processing {file_name}")
            
            try:
                # Read all sheets from the Excel file
                excel_data = pd.read_excel(excel_file, sheet_name=None)
                
                # Process each expected worksheet
                for sheet_name in worksheet_names:
                    if sheet_name in excel_data:
                        df = excel_data[sheet_name]
                        
                        # Check if the required columns exist
                        if 'Author Name' in df.columns and 'Email' in df.columns:
                            # Select only the required columns
                            df_filtered = df[['Author Name', 'Email']].copy()

                            # Remove empty rows
                            df_filtered = df_filtered.dropna(subset=['Email'])

                            # Add to merged data
                            merged_data[sheet_name].append(df_filtered)
                            
                            console.print(f"  [green]✓[/green] {sheet_name}: {len(df_filtered)} rows from {file_name}")
                        else:
                            console.print(f"  [yellow]⚠[/yellow] {sheet_name}: Missing required columns in {file_name}")
                    else:
                        console.print(f"  [yellow]⚠[/yellow] {sheet_name}: Sheet not found in {file_name}")
                
            except Exception as e:
                console.print(f"  [red]✗[/red] Error processing {file_name}: {str(e)}")
            
            progress.advance(main_task)
    
    # Save merged data for each worksheet type
    console.print("\n[blue]Saving merged files...[/blue]")
    
    summary_data = []
    
    for sheet_name, data_list in merged_data.items():
        if data_list:
            # Combine all dataframes for this sheet type
            combined_df = pd.concat(data_list, ignore_index=True)
            
            # Remove duplicates based on Email column
            initial_count = len(combined_df)
            combined_df = combined_df.drop_duplicates(subset=['Email'], keep='first')
            final_count = len(combined_df)
            duplicates_removed = initial_count - final_count
            
            # Save to Excel file
            output_file = os.path.join(output_dir, f"{sheet_name}.xlsx")
            combined_df.to_excel(output_file, index=False, encoding='utf-8-sig')
            
            console.print(f"[green]✓[/green] Saved {sheet_name}.xlsx: {final_count} unique records")
            if duplicates_removed > 0:
                console.print(f"  [yellow]  Removed {duplicates_removed} duplicate emails[/yellow]")
            
            summary_data.append({
                'Worksheet': sheet_name,
                'Total_Records': final_count,
                'Duplicates_Removed': duplicates_removed,
                'Output_File': f"{sheet_name}.xlsx"
            })
        else:
            console.print(f"[red]✗[/red] No data found for {sheet_name}")
    
    # Create summary report
    if summary_data:
        summary_df = pd.DataFrame(summary_data)
        summary_file = os.path.join(output_dir, "Merge_Summary.xlsx")
        summary_df.to_excel(summary_file, index=False, encoding='utf-8-sig')
        console.print(f"\n[green]✓[/green] Summary report saved: Merge_Summary.xlsx")
    
    # Display final summary
    console.print("\n" + "="*60)
    console.print(Panel.fit(
        Text("Merge Process Completed!", style="bold green"),
        title="Summary",
        border_style="green"
    ))
    
    for item in summary_data:
        console.print(f"[blue]{item['Worksheet']}:[/blue] {item['Total_Records']} records")

if __name__ == "__main__":
    console.print(Panel.fit(
        Text("Excel Worksheet Merger", style="bold blue"),
        subtitle="Merging worksheets from multiple Excel workbooks",
        border_style="blue"
    ))
    
    merge_excel_worksheets()
    
    console.print("\n[green]Process completed! Check the 'merged' directory for output files.[/green]")
